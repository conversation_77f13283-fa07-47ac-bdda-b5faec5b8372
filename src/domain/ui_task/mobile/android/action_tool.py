#!/usr/bin/env python3
"""
设备操作工具模块 - 重构优化版本
"""

import re
from dataclasses import dataclass
from time import sleep
from typing import Dict, Any, Optional, Tuple

from loguru import logger

from src.domain.ui_task.mobile.android.screenshot_manager import execute_adb
from src.domain.ui_task.mobile.android.native_adb_utils import native_adb
from src.domain.ui_task.mobile.android.keyboard_manager import get_keyboard_manager
from src.schema.action_types import ActionType, SwipeDirection
from src.schema.constants import SystemConstants, MessageConstants


@dataclass
class ActionResult:
    """操作结果数据类"""
    status: str
    action: str
    device: str
    message: str
    data: Optional[Dict[str, Any]] = None


class ActionParser:
    """统一的动作解析器"""

    # 简化的正则表达式模式
    PATTERNS = {
        'point_new': r'<point>(\d+)\s+(\d+)</point>',
        'point_old': r'\((\d+),\s*(\d+)\)',
        'content': r'content=["\']([^"\']*)["\']',
        'content_flexible': r'content=([^)]+)',
        'number': r'(\d+)',
    }

    def __init__(self, device: str):
        """初始化解析器

        Args:
            device: 设备ID，用于获取屏幕尺寸进行坐标归一化
        """
        self.device = device

    def parse(self, action_str: str) -> Optional[Dict[str, Any]]:
        """解析动作字符串"""
        action_str = action_str.strip().lower()

        # 按优先级尝试解析各种动作
        parsers = [
            self._parse_click,
            self._parse_long_press,
            self._parse_drag,
            self._parse_scroll,
            self._parse_type,
            self._parse_delete,
            self._parse_simple_actions,
            self._parse_finished,
            self._parse_failed,
        ]

        for parser in parsers:
            result = parser(action_str)
            if result:
                return result

        logger.warning(f"⚠️ Failed to parse action: {action_str}")
        return None

    def _normalize_coordinates(self, x: int, y: int) -> Tuple[int, int]:
        """
        归一化坐标处理
        模型输出的坐标需要除以1000然后乘以界面宽高，并进行密度调整

        Args:
            x: 原始x坐标
            y: 原始y坐标

        Returns:
            归一化并调整密度后的坐标 (x, y)
        """
        try:
            # 获取屏幕尺寸
            screen_width, screen_height = ScreenUtils.get_screen_size(self.device)

            # 第一步：归一化处理：坐标除以1000然后乘以界面宽高
            normalized_x = int((x / 1000.0) * screen_width)
            normalized_y = int((y / 1000.0) * screen_height)

            # 确保坐标在屏幕范围内
            normalized_x = max(0, min(normalized_x, screen_width))
            normalized_y = max(0, min(normalized_y, screen_height))

            logger.debug(f"🎯 坐标归一化: ({x}, {y}) -> ({normalized_x}, {normalized_y}) "
                         f"(屏幕尺寸: {screen_width}x{screen_height})")
            return normalized_x, normalized_y
        except Exception as e:
            logger.error(f"⚠️ 坐标归一化失败: {str(e)}, 使用原始坐标")
            return x, y

    def _extract_coordinates(self, text: str) -> Optional[Tuple[int, int]]:
        """提取坐标信息并进行归一化处理"""
        # 尝试新格式 <point>x y</point>
        match = re.search(self.PATTERNS['point_new'], text)
        if match:
            raw_x, raw_y = int(match.group(1)), int(match.group(2))
            return self._normalize_coordinates(raw_x, raw_y)

        # 尝试旧格式 (x,y)
        match = re.search(self.PATTERNS['point_old'], text)
        if match:
            raw_x, raw_y = int(match.group(1)), int(match.group(2))
            return self._normalize_coordinates(raw_x, raw_y)

        return None

    def _extract_content(self, text: str) -> Optional[str]:
        """提取内容信息"""
        # 尝试标准格式
        match = re.search(self.PATTERNS['content'], text)
        if match:
            return match.group(1)

        # 尝试灵活格式
        if 'content=' in text:
            start = text.find('content=') + 8
            content_part = text[start:].strip()

            # 处理各种引号情况
            for quote in ['"', "'"]:
                if content_part.startswith(quote):
                    end_pos = content_part.find(quote, 1)
                    if end_pos > 0:
                        return content_part[1:end_pos]
                    elif content_part.endswith(')'):
                        return content_part[1:-1]

            # 无引号情况
            if content_part.endswith(')'):
                return content_part[:-1]
            return content_part

        return None

    def _parse_click(self, action_str: str) -> Optional[Dict[str, Any]]:
        """解析点击动作"""
        if 'click' not in action_str:
            return None

        coords = self._extract_coordinates(action_str)
        if coords:
            return {
                "action": ActionType.CLICK.value,
                "x": coords[0],
                "y": coords[1],
            }
        return None

    def _parse_long_press(self, action_str: str) -> Optional[Dict[str, Any]]:
        """解析长按动作"""
        if 'long_press' not in action_str:
            return None

        coords = self._extract_coordinates(action_str)
        if coords:
            return {
                "action": ActionType.LONG_PRESS.value,
                "x": coords[0],
                "y": coords[1],
            }
        return None

    def _parse_drag(self, action_str: str) -> Optional[Dict[str, Any]]:
        """解析拖拽动作"""
        if 'drag' not in action_str:
            return None

        # 查找所有坐标点
        coords = re.findall(self.PATTERNS['point_new'], action_str)
        if len(coords) >= 2:
            # 对起始和结束坐标都进行归一化处理
            start_x, start_y = self._normalize_coordinates(int(coords[0][0]), int(coords[0][1]))
            end_x, end_y = self._normalize_coordinates(int(coords[1][0]), int(coords[1][1]))
            return {
                "action": ActionType.DRAG.value,
                "start_x": start_x,
                "start_y": start_y,
                "end_x": end_x,
                "end_y": end_y,
            }

        coords = re.findall(self.PATTERNS['point_old'], action_str)
        if len(coords) >= 2:
            # 对起始和结束坐标都进行归一化处理
            start_x, start_y = self._normalize_coordinates(int(coords[0][0]), int(coords[0][1]))
            end_x, end_y = self._normalize_coordinates(int(coords[1][0]), int(coords[1][1]))
            return {
                "action": ActionType.DRAG.value,
                "start_x": start_x,
                "start_y": start_y,
                "end_x": end_x,
                "end_y": end_y,
            }
        return None

    def _parse_scroll(self, action_str: str) -> Optional[Dict[str, Any]]:
        """解析滚动动作"""
        if 'scroll' not in action_str:
            return None

        result = {"action": ActionType.SCROLL.value}

        # 提取坐标
        coords = self._extract_coordinates(action_str)
        if coords:
            result["start_x"] = coords[0]
            result["start_y"] = coords[1]

        # 提取方向
        for direction in ['up', 'down', 'left', 'right']:
            if direction in action_str:
                result["direction"] = direction
                break

        return result if len(result) > 1 else None

    def _parse_type(self, action_str: str) -> Optional[Dict[str, Any]]:
        """解析输入动作"""
        if 'type' not in action_str:
            return None

        content = self._extract_content(action_str)
        if content is not None:
            return {
                "action": ActionType.TYPE.value,
                "content": content,
            }
        return None

    def _parse_delete(self, action_str: str) -> Optional[Dict[str, Any]]:
        """解析删除动作"""
        if 'delete' not in action_str:
            return None

        # 尝试提取数量
        match = re.search(self.PATTERNS['number'], action_str)
        count = int(match.group(1)) if match else 1

        return {
            "action": ActionType.DELETE.value,
            "content": count,
        }

    def _parse_simple_actions(self, action_str: str) -> Optional[Dict[str, Any]]:
        """解析简单动作"""
        # 特殊处理wait动作，因为它可能有参数
        if 'wait' in action_str:
            result = {"action": ActionType.WAIT.value}

            # 尝试提取seconds参数
            seconds_match = re.search(r'seconds=(\d+)', action_str)
            if seconds_match:
                result["seconds"] = int(seconds_match.group(1))
            else:
                # 如果没有指定参数，使用默认值5秒
                result["seconds"] = 5

            return result

        # 处理其他简单动作
        simple_actions = {
            'back': ActionType.BACK.value,
            'enter': ActionType.ENTER.value,
        }

        for keyword, action_type in simple_actions.items():
            if keyword in action_str:
                return {"action": action_type}
        return None

    def _parse_finished(self, action_str: str) -> Optional[Dict[str, Any]]:
        """解析完成动作"""
        if 'finished' not in action_str:
            return None

        content = self._extract_content(action_str)
        return {
            "action": ActionType.FINISHED.value,
            "content": content or "",
        }

    def _parse_failed(self, action_str: str) -> Optional[Dict[str, Any]]:
        """解析失败动作"""
        if 'failed' not in action_str:
            return None

        content = self._extract_content(action_str)
        return {
            "action": ActionType.FAILED.value,
            "content": content or "",
        }


class ActionValidator:
    """动作验证器"""

    VALIDATION_RULES = {
        ActionType.CLICK.value: lambda a: "x" in a and "y" in a,
        ActionType.LONG_PRESS.value: lambda a: "x" in a and "y" in a,
        ActionType.DRAG.value: lambda a: all(k in a for k in ["start_x", "start_y", "end_x", "end_y"]),
        ActionType.SCROLL.value: lambda a: "direction" in a or ("start_x" in a and "start_y" in a),
        ActionType.TYPE.value: lambda a: "content" in a,
        ActionType.DELETE.value: lambda a: True,
        ActionType.BACK.value: lambda a: True,
        ActionType.WAIT.value: lambda a: True,
        ActionType.ENTER.value: lambda a: True,
        ActionType.FINISHED.value: lambda a: True,
        ActionType.FAILED.value: lambda a: True,
    }

    @classmethod
    def validate(cls, parsed_action: Dict[str, Any]) -> bool:
        """验证解析后的动作是否有效"""
        if not isinstance(parsed_action, dict):
            return False

        action_type = parsed_action.get("action", "")
        validator = cls.VALIDATION_RULES.get(action_type)
        return validator(parsed_action) if validator else False


class ScreenUtils:
    """屏幕相关工具类"""

    # 屏幕尺寸缓存，避免重复ADB调用
    _screen_size_cache = {}

    @staticmethod
    def get_screen_size(device: str) -> Tuple[int, int]:
        """获取屏幕尺寸（带缓存）"""
        # 检查缓存
        if device in ScreenUtils._screen_size_cache:
            cached_size = ScreenUtils._screen_size_cache[device]
            logger.debug(f"📱 Using cached screen size for {device}: {cached_size[0]}x{cached_size[1]}")
            return cached_size

        try:
            # 使用原生adb获取屏幕尺寸
            width, height = native_adb.get_screen_size(device)

            # 缓存结果
            ScreenUtils._screen_size_cache[device] = (width, height)
            return width, height

        except Exception as e:
            logger.error(f"⚠️ Error getting screen size for {device}: {str(e)}, using default: 1080x1920")
            # 缓存默认值，避免重复失败
            ScreenUtils._screen_size_cache[device] = (1080, 1920)
            return 1080, 1920

    @staticmethod
    def clear_screen_size_cache(device: str = None):
        """清除屏幕尺寸缓存"""
        if device:
            if device in ScreenUtils._screen_size_cache:
                del ScreenUtils._screen_size_cache[device]
                logger.info(f"📱 Cleared screen size cache for device: {device}")
        else:
            ScreenUtils._screen_size_cache.clear()
            logger.info("📱 Cleared all screen size cache")

    @staticmethod
    def calculate_scroll_coordinates(
            device: str,
            direction: str,
            start_x: Optional[int] = None,
            start_y: Optional[int] = None
    ) -> Tuple[int, int, int, int]:
        """
        计算滚动坐标

        将起始点滑动到屏幕边缘，最大化滑动效果
        - 向上滑动：滑到屏幕顶部 (y=0)
        - 向下滑动：滑到屏幕底部 (y=screen_height)
        - 向左滑动：滑到屏幕左边 (x=0)
        - 向右滑动：滑到屏幕右边 (x=screen_width)
        """
        screen_width, screen_height = ScreenUtils.get_screen_size(device)

        # 默认使用屏幕中心作为起始点
        if start_x is None or start_y is None:
            start_x, start_y = screen_width // 2, screen_height // 2

        # 计算结束坐标 - 滑动到屏幕边缘
        # 定义滑动方向映射表，将滑动方向枚举值映射到对应的屏幕坐标偏移量
        # UP: Y坐标减少200像素，实现向上滑动
        # DOWN: Y坐标增加200像素，实现向下滑动  
        # LEFT: X坐标减少200像素，实现向左滑动
        # RIGHT: X坐标增加200像素，实现向右滑动
        direction_map = {
            SwipeDirection.UP.value: (start_x, start_y - int(screen_height/8)),
            SwipeDirection.DOWN.value: (start_x, start_y + int(screen_height/8)),
            SwipeDirection.LEFT.value: (start_x - 250, start_y),
            SwipeDirection.RIGHT.value: (start_x + 250, start_y),
        }

        end_x, end_y = direction_map.get(direction, (start_x, start_y))
        return start_x, start_y, end_x, end_y


class TextUtils:
    """文本处理工具类"""

    @staticmethod
    def escape_for_adb(text: str) -> str:
        """为ADB shell命令转义特殊字符"""
        if not text:
            return text

        # 转义映射表
        escape_map = {
            '\\': '\\\\',
            '"': '\\"',
            "'": "\\'",
            '#': '\\#',
            '&': '\\&',
            '$': '\\$',
            '`': '\\`',
            '(': '\\(',
            ')': '\\)',
            '[': '\\[',
            ']': '\\]',
            '{': '\\{',
            '}': '\\}',
            '|': '\\|',
            ';': '\\;',
            '<': '\\<',
            '>': '\\>',
            '*': '\\*',
            '?': '\\?',
            ' ': '\\ ',
        }

        result = text
        for char, escaped in escape_map.items():
            result = result.replace(char, escaped)
        return result


class ActionExecutor:
    """动作执行器"""

    def __init__(self, device: str):
        self.device = device
        self.parser = ActionParser(device)
        self.validator = ActionValidator()

    def execute(self, action_str: str, device: str = None) -> ActionResult:
        """执行动作字符串"""
        # 使用传入的设备或默认设备
        target_device = device or self.device

        # 如果设备发生变化，重新创建解析器
        if target_device != self.device:
            self.device = target_device
            self.parser = ActionParser(target_device)

        # 解析动作
        parsed_action = self.parser.parse(action_str)
        if not parsed_action:
            return ActionResult(
                status="error",
                action="unknown",
                device=target_device,
                message=f"Failed to parse action: {action_str}"
            )

        # 验证动作
        if not self.validator.validate(parsed_action):
            return ActionResult(
                status="error",
                action=parsed_action.get("action", "unknown"),
                device=target_device,
                message=f"Invalid action: {parsed_action}"
            )

        # 执行动作
        return self._execute_parsed_action(parsed_action, target_device)

    def _execute_parsed_action(self, parsed_action: Dict[str, Any], device: str) -> ActionResult:
        """执行已解析的动作"""
        action_type = parsed_action.get("action")

        try:
            if action_type == ActionType.CLICK.value:
                return self._execute_click(parsed_action, device)
            elif action_type == ActionType.LONG_PRESS.value:
                return self._execute_long_press(parsed_action, device)
            elif action_type == ActionType.TYPE.value:
                return self._execute_type(parsed_action, device)
            elif action_type == ActionType.DELETE.value:
                return self._execute_delete(parsed_action, device)
            elif action_type == ActionType.DRAG.value:
                return self._execute_drag(parsed_action, device)
            elif action_type == ActionType.SCROLL.value:
                return self._execute_scroll(parsed_action, device)
            elif action_type == ActionType.WAIT.value:
                return self._execute_wait(parsed_action, device)
            elif action_type == ActionType.BACK.value:
                return self._execute_back(parsed_action, device)
            elif action_type == ActionType.ENTER.value:
                return self._execute_enter(parsed_action, device)
            elif action_type == ActionType.FINISHED.value:
                return ActionResult(
                    status="success",
                    action=action_type,
                    device=device,
                    message="Task finished",
                    data={"content": parsed_action.get("content", "")}
                )
            elif action_type == ActionType.FAILED.value:
                return ActionResult(
                    status="failed",
                    action=action_type,
                    device=device,
                    message="Task failed",
                    data={"content": parsed_action.get("content", "")}
                )
            else:
                return ActionResult(
                    status="error",
                    action=action_type,
                    device=device,
                    message=f"Unsupported action type: {action_type}"
                )

        except Exception as e:
            return ActionResult(
                status="error",
                action=action_type,
                device=device,
                message=f"Exception during action execution: {str(e)}"
            )

    def _execute_adb_command(self, command: str, action_type: str, device: str) -> ActionResult:
        """执行ADB命令的通用方法"""
        result = execute_adb(command)
        if result != "ERROR":
            return ActionResult(
                status="success",
                action=action_type,
                device=device,
                message=MessageConstants.SUCCESS_ACTION,
                data={"adb_result": result}
            )
        else:
            return ActionResult(
                status="error",
                action=action_type,
                device=device,
                message=MessageConstants.ERROR_ACTION_FAILED,
                data={"adb_command": command}
            )

    def _execute_shell_command(self, shell_command: str, action_type: str, device: str) -> ActionResult:
        """直接执行Shell命令的方法（使用原生adb），对执行动作启用重试"""
        try:
            # 对执行动作的命令启用重试机制
            success, result = native_adb.shell_command(device, shell_command, timeout=10, enable_retry=True)
            if success:
                return ActionResult(
                    status="success",
                    action=action_type,
                    device=device,
                    message=MessageConstants.SUCCESS_ACTION,
                    data={"shell_result": result.strip()}
                )
            else:
                return ActionResult(
                    status="error",
                    action=action_type,
                    device=device,
                    message=f"Shell command failed: {result}",
                    data={"shell_command": shell_command}
                )
        except Exception as e:
            return ActionResult(
                status="error",
                action=action_type,
                device=device,
                message=f"Shell command failed: {str(e)}",
                data={"shell_command": shell_command}
            )

    def _execute_click(self, parsed_action: Dict[str, Any], device: str) -> ActionResult:
        """执行点击动作"""
        x, y = parsed_action["x"], parsed_action["y"]
        shell_command = f"input tap {x} {y}"
        result = self._execute_shell_command(shell_command, ActionType.CLICK.value, device)
        if result.status == "success":
            result.data = {"clicked_element": {"x": x, "y": y}}
        return result

    def _execute_long_press(self, parsed_action: Dict[str, Any], device: str) -> ActionResult:
        """执行长按动作"""
        x, y = parsed_action["x"], parsed_action["y"]
        duration = SystemConstants.DEFAULT_DURATION
        shell_command = f"input swipe {x} {y} {x} {y} {duration}"
        result = self._execute_shell_command(shell_command, ActionType.LONG_PRESS.value, device)
        if result.status == "success":
            result.data = {"long_pressed_element": {"x": x, "y": y, "duration": duration}}
        return result

    def _execute_type(self, parsed_action: Dict[str, Any], device: str) -> ActionResult:
        """执行输入动作"""
        content = parsed_action["content"]

        # 获取当前激活的输入法
        try:
            keyboard_manager = get_keyboard_manager(device)
            current_ime = keyboard_manager.get_current_ime()
            logger.debug(f"🎹 Current IME: {current_ime}")

            # 根据不同的输入法使用不同的命令
            if current_ime == "com.github.uiautomator/.FastInputIME":
                # FastInputIME 使用 base64 编码
                shell_command = f'am broadcast -a ADB_INPUT_TEXT --es text $(printf "%s" "{content}" | base64)'
                logger.debug(f"🎹 Using FastInputIME command for input: {content}")
            elif current_ime == "com.github.uiautomator/.AdbKeyboard":
                # AdbKeyboard 使用 msg 参数
                shell_command = f'am broadcast -a ADB_INPUT_TEXT --es msg "{content}"'
                logger.debug(f"🎹 Using AdbKeyboard command for input: {content}")
            else:
                # 默认使用 FastInputIME 的命令格式
                shell_command = f'am broadcast -a ADB_INPUT_TEXT --es text $(printf "%s" "{content}" | base64)'
                logger.warning(f"🎹 Unknown IME ({current_ime}), using default FastInputIME command for input: {content}")

        except Exception as e:
            # 如果获取输入法失败，使用默认命令
            shell_command = f'am broadcast -a ADB_INPUT_TEXT --es text $(printf "%s" "{content}" | base64)'
            logger.warning(f"🎹 Failed to get current IME ({str(e)}), using default FastInputIME command for input: {content}")

        result = self._execute_shell_command(shell_command, ActionType.TYPE.value, device)
        if result.status == "success":
            result.data = {"input_text": content}
        return result

    def _execute_delete(self, parsed_action: Dict[str, Any], device: str) -> ActionResult:
        """执行删除动作"""
        delete_count = parsed_action.get("content", 1)

        for i in range(delete_count):
            shell_command = "input keyevent KEYCODE_DEL"
            result = self._execute_shell_command(shell_command, ActionType.DELETE.value, device)
            if result.status == "error":
                return ActionResult(
                    status="error",
                    action=ActionType.DELETE.value,
                    device=device,
                    message=f"Delete operation failed at character {i + 1}"
                )
            sleep(0.1)  # 短暂延迟

        return ActionResult(
            status="success",
            action=ActionType.DELETE.value,
            device=device,
            message=f"Successfully deleted {delete_count} character(s)",
            data={"delete": {"count": delete_count}}
        )

    def _execute_drag(self, parsed_action: Dict[str, Any], device: str) -> ActionResult:
        """执行拖拽动作"""
        start_x, start_y = parsed_action["start_x"], parsed_action["start_y"]
        end_x, end_y = parsed_action["end_x"], parsed_action["end_y"]
        duration = SystemConstants.DEFAULT_DURATION

        shell_command = f"input swipe {start_x} {start_y} {end_x} {end_y} {duration}"
        result = self._execute_shell_command(shell_command, ActionType.DRAG.value, device)
        if result.status == "success":
            result.data = {
                "drag": {
                    "start": {"x": start_x, "y": start_y},
                    "end": {"x": end_x, "y": end_y},
                    "duration": duration
                }
            }
        return result

    def _execute_scroll(self, parsed_action: Dict[str, Any], device: str) -> ActionResult:
        """执行滚动动作"""

        direction = parsed_action.get("direction")
        start_x = parsed_action.get("start_x")
        start_y = parsed_action.get("start_y")
        end_x = parsed_action.get("end_x")
        end_y = parsed_action.get("end_y")

        screen_width, screen_height = ScreenUtils.get_screen_size(device)

        if start_x >= screen_width - 50:
            start_x = screen_width - 50

        if start_x <= 50:
            start_x = 50

        if start_y >= screen_height - 50:
            start_y = screen_height - 50

        if start_y <= 50:
            start_y = 50

        # 如果有明确的起始和结束坐标
        if all(coord is not None for coord in [start_x, start_y, end_x, end_y]):
            pass  # 使用现有坐标
        elif direction:
            # 根据方向计算坐标
            start_x, start_y, end_x, end_y = ScreenUtils.calculate_scroll_coordinates(
                device, direction, start_x, start_y
            )
        else:
            return ActionResult(
                status="error",
                action=ActionType.SCROLL.value,
                device=device,
                message="Scroll action requires either start/end coordinates or direction"
            )

        duration = SystemConstants.DEFAULT_DURATION

        shell_command = f"input swipe {start_x} {start_y} {end_x} {end_y} {duration}"
        result = self._execute_shell_command(shell_command, ActionType.SCROLL.value, device)
        if result.status == "success":
            result.data = {
                "scroll": {
                    "start": {"x": start_x, "y": start_y},
                    "end": {"x": end_x, "y": end_y},
                    "direction": direction or "custom"
                }
            }
        return result

    def _execute_wait(self, parsed_action: Dict[str, Any], device: str) -> ActionResult:
        """执行等待动作"""
        seconds = parsed_action.get("seconds", 5)  # 默认等待5秒
        sleep(seconds)
        return ActionResult(
            status="success",
            action=ActionType.WAIT.value,
            device=device,
            message=f"Wait action completed ({seconds} seconds)",
            data={"wait": {"seconds": seconds}}
        )

    def _execute_back(self, parsed_action: Dict[str, Any], device: str) -> ActionResult:
        """执行返回动作"""
        shell_command = "input keyevent KEYCODE_BACK"
        return self._execute_shell_command(shell_command, ActionType.BACK.value, device)

    def _execute_enter(self, parsed_action: Dict[str, Any], device: str) -> ActionResult:
        """执行回车动作"""
        shell_command = "input keyevent KEYCODE_ENTER"
        return self._execute_shell_command(shell_command, ActionType.ENTER.value, device)


# 兼容性接口 - 保持向后兼容
def get_screen_size(device: str) -> Tuple[int, int]:
    """兼容性接口：获取屏幕尺寸"""
    return ScreenUtils.get_screen_size(device)


def parse_action_with_coordinates(action_str: str, device: str) -> Optional[
    Dict[str, Any]]:
    """兼容性接口：解析带坐标的操作字符串"""
    parser = ActionParser(device)
    return parser.parse(action_str)


def validate_action(parsed_action: Dict[str, Any]) -> bool:
    """兼容性接口：验证操作是否有效"""
    validator = ActionValidator()
    return validator.validate(parsed_action)


def execute_simple_action(action_str: str, device: str) -> Dict[str, Any]:
    """兼容性接口：执行简单操作"""
    executor = ActionExecutor(device)
    result = executor.execute(action_str, device)

    # 转换为字典格式以保持兼容性
    result_dict = {
        "status": result.status,
        "action": result.action,
        "device": result.device,
        "message": result.message,
    }

    if result.data:
        result_dict.update(result.data)

    return result_dict
